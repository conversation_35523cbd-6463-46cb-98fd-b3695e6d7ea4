#!/usr/bin/env python3
"""
Car Ownership Analyzer
Analyzes social media posts to determine car ownership and extract brand, model, status information.
"""

import pandas as pd
import re
import json
import time
import os
from typing import List, Dict, Tuple, Optional
from tqdm import tqdm
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv('local.env')

# Configure Gemini API
gemini_api_key = os.getenv('GEMINI_API_KEY')
if gemini_api_key:
    genai.configure(api_key=gemini_api_key)
    model = genai.GenerativeModel('gemini-pro')
else:
    print("Warning: GEMINI_API_KEY not found in local.env")

def extract_profile_and_post(text: str) -> Tuple[str, str]:
    """Extract profile and post content from the combined text."""
    if pd.isna(text):
        return "", ""

    text = str(text)

    # Extract profile section
    profile_match = re.search(r'【プロフィール】(.*?)(?=【投稿|$)', text, re.DOTALL)
    profile_content = profile_match.group(1).strip() if profile_match else ""

    # Extract post content section
    post_match = re.search(r'【投稿内容】(.*?)$', text, re.DOTALL)
    post_content = post_match.group(1).strip() if post_match else ""

    return profile_content, post_content

def analyze_car_ownership_rule_based(profile_content: str, post_content: str, car_brands: set, car_models: set) -> Dict:
    """Rule-based approach to analyze car ownership."""
    combined_text = (profile_content + " " + post_content).lower()

    # Ownership indicators
    ownership_patterns = [
        r'my\s+([a-zA-Z0-9\s]+)\s+car',
        r'i\s+own\s+([a-zA-Z0-9\s]+)',
        r'i\s+have\s+([a-zA-Z0-9\s]+)',
        r'i\s+bought\s+([a-zA-Z0-9\s]+)',
        r'i\s+purchased\s+([a-zA-Z0-9\s]+)',
        r'driving\s+my\s+([a-zA-Z0-9\s]+)',
        r'my\s+([a-zA-Z0-9\s]+)',
        r'車\s*を\s*持っている',
        r'車\s*を\s*買った',
        r'愛車',
    ]

    found_cars = []

    # Check for car mentions
    for brand in car_brands:
        brand_lower = brand.lower()
        if brand_lower in combined_text:
            # Find models for this brand
            for model in car_models:
                model_lower = model.lower()
                if model_lower in combined_text:
                    # Check if it's in ownership context
                    ownership_found = False
                    for pattern in ownership_patterns:
                        if re.search(pattern, combined_text):
                            ownership_found = True
                            break

                    # Also check for direct mentions that suggest ownership
                    if (f"my {model_lower}" in combined_text or
                        f"my {brand_lower} {model_lower}" in combined_text or
                        f"{model_lower} car" in combined_text):
                        ownership_found = True

                    if ownership_found:
                        profile_only = "○" if model_lower in profile_content.lower() else ""
                        found_cars.append({
                            "brand": brand,
                            "model": model,
                            "status": "既存",
                            "profile_only": profile_only
                        })
                        break  # Found a match for this brand, move to next brand

    return {
        "has_car": len(found_cars) > 0,
        "cars": found_cars
    }

def create_car_analysis_prompt_gemini(profile_content: str, post_content: str, car_brands: set, car_models: set) -> str:
    """Create a prompt for Gemini to analyze car ownership from profile and post content."""
    brands_list = ", ".join(sorted(list(car_brands))[:20])  # Limit to first 20 for prompt size
    models_list = ", ".join(sorted(list(car_models))[:30])  # Limit to first 30 for prompt size

    prompt = f"""
You are analyzing social media content to determine car ownership. Please analyze the following profile and post content to extract car ownership information.

PROFILE CONTENT:
{profile_content}

POST CONTENT:
{post_content}

TASK:
1. Determine if the person owns any cars based on the content
2. Extract car brand(s) and model(s) if ownership is indicated
3. Determine if the car information can be determined from PROFILE ONLY or requires POST CONTENT

REFERENCE CAR BRANDS (examples): {brands_list}...
REFERENCE CAR MODELS (examples): {models_list}...

RULES:
- Look for ownership indicators like "my car", "I own", "I have", "I bought", "I purchased", etc.
- If multiple cars are mentioned, separate them with commas
- Status should be "既存" if they currently own the car
- Mark "○" for profile-only determination if car info can be determined from profile alone
- Leave profile-only determination empty if post content is needed

Please respond in JSON format only:
{{
    "has_car": true/false,
    "cars": [
        {{
            "brand": "brand_name",
            "model": "model_name",
            "status": "既存",
            "profile_only": "○" or ""
        }}
    ]
}}
"""
    return prompt

def analyze_car_ownership_with_gemini(profile_content: str, post_content: str, car_brands: set, car_models: set) -> Dict:
    """Use Gemini API to analyze car ownership from profile and post content."""
    if not gemini_api_key:
        print("Gemini API key not available, falling back to rule-based approach")
        return analyze_car_ownership_rule_based(profile_content, post_content, car_brands, car_models)

    prompt = create_car_analysis_prompt_gemini(profile_content, post_content, car_brands, car_models)

    try:
        response = model.generate_content(prompt)
        result_text = response.text.strip()

        # Try to extract JSON from the response
        try:
            # Sometimes the response might have extra text, so try to find JSON
            json_start = result_text.find('{')
            json_end = result_text.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_text = result_text[json_start:json_end]
                result = json.loads(json_text)
                return result
            else:
                print(f"No JSON found in response: {result_text}")
                return {"has_car": False, "cars": []}
        except json.JSONDecodeError as e:
            print(f"Failed to parse JSON: {result_text}, Error: {e}")
            return {"has_car": False, "cars": []}

    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return {"has_car": False, "cars": []}

def process_dataframe(df: pd.DataFrame, car_brands: set, car_models: set, use_gemini: bool = True) -> pd.DataFrame:
    """Process the entire dataframe to extract car ownership information."""
    # Initialize the columns (they already exist but are empty)
    # Convert to string type to avoid dtype issues
    df['ブランド'] = df['ブランド'].astype(str).fillna('')
    df['車種\u3000'] = df['車種\u3000'].astype(str).fillna('')  # Unicode full-width space
    df['状態'] = df['状態'].astype(str).fillna('')
    df['プロフィールのみで判断'] = df['プロフィールのみで判断'].astype(str).fillna('')

    # Process each row
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing rows"):
        profile_content, post_content = extract_profile_and_post(row['post_line_with_bio'])

        if use_gemini:
            result = analyze_car_ownership_with_gemini(profile_content, post_content, car_brands, car_models)
        else:
            result = analyze_car_ownership_rule_based(profile_content, post_content, car_brands, car_models)

        if result['has_car'] and result['cars']:
            # Handle multiple cars by joining with commas
            brands = [car['brand'] for car in result['cars']]
            models = [car['model'] for car in result['cars']]
            statuses = [car['status'] for car in result['cars']]
            profile_only_flags = [car['profile_only'] for car in result['cars']]

            df.at[idx, 'ブランド'] = ', '.join(brands)
            df.at[idx, '車種\u3000'] = ', '.join(models)  # Unicode full-width space
            df.at[idx, '状態'] = ', '.join(statuses)
            df.at[idx, 'プロフィールのみで判断'] = ', '.join(profile_only_flags)

        # Add delay for API rate limiting
        if use_gemini:
            time.sleep(0.5)  # 0.5 second delay for Gemini API

    return df

def main():
    """Main execution function."""
    print("Loading data files...")

    # Load the data files
    df = pd.read_csv('データ.csv')
    car_models_df = pd.read_csv('機種リスト.csv')

    print(f"Main data shape: {df.shape}")
    print(f"Car models data shape: {car_models_df.shape}")

    # Create car brands and models sets
    car_brands = set()
    car_models = set()

    for _, row in car_models_df.iterrows():
        if pd.notna(row['ブランド']) and pd.notna(row['モデル']):
            brand = row['ブランド'].strip()
            model = row['モデル'].strip()
            car_brands.add(brand)
            car_models.add(model)

    print(f"Total unique brands: {len(car_brands)}")
    print(f"Total unique models: {len(car_models)}")

    # Process the dataframe
    print("Processing dataframe with Gemini API...")
    processed_df = process_dataframe(df.copy(), car_brands, car_models, use_gemini=True)

    # Save the results
    output_filename = 'データ_processed.csv'
    processed_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
    print(f"Results saved to {output_filename}")

    # Show summary statistics
    cars_found = processed_df[(processed_df['ブランド'] != '') & (processed_df['ブランド'] != 'nan')].shape[0]
    total_rows = processed_df.shape[0]
    print(f"\nSummary:")
    print(f"Total rows processed: {total_rows}")
    print(f"Rows with car ownership detected: {cars_found}")
    print(f"Detection rate: {cars_found/total_rows*100:.1f}%")

    # Show top brands and models found
    brand_counts = {}
    for brands_str in processed_df['ブランド']:
        if brands_str and brands_str != '' and brands_str != 'nan':
            brands = [b.strip() for b in str(brands_str).split(',')]
            for brand in brands:
                if brand and brand != 'nan':
                    brand_counts[brand] = brand_counts.get(brand, 0) + 1

    if brand_counts:
        print("\nTop 10 Car Brands Found:")
        sorted_brands = sorted(brand_counts.items(), key=lambda x: x[1], reverse=True)
        for brand, count in sorted_brands[:10]:
            print(f"  {brand}: {count}")

if __name__ == "__main__":
    main()