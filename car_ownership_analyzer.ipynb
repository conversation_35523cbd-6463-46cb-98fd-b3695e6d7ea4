﻿{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Car Ownership Analysis from Social Media Posts\n",
    "\n",
    "This notebook analyzes social media posts to determine car ownership and extract:\n",
    "- ブランド (Brand)\n",
    "- 車種 (Car Model)\n",
    "- 状態 (Status: 既存 for existing ownership)\n",
    "- プロフィールのみで判断 (Profile-only determination:  if determined from profile only)\n",
    "\n",
    "The analysis looks at both 【プロフィール】(Profile) and 【投稿内容】(Post Content) sections."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import re\n",
    "import openai\n",
    "from typing import List, Dict, Tuple, Optional\n",
    "import json\n",
    "import time\n",
    "from tqdm import tqdm\n",
    "\n",
    "# Set your OpenAI API key\n",
    "# openai.api_key = \"your-api-key-here\"  # Replace with your actual API key\n",
    "\n",
    "print(\"Libraries imported successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the data files\n",
    "df = pd.read_csv('データ.csv')\n",
    "car_models_df = pd.read_csv('機種リスト.csv')\n",
    "\n",
    "print(f\"Main data shape: {df.shape}\")\n",
    "print(f\"Car models data shape: {car_models_df.shape}\")\n",
    "print(\"\\nFirst few rows of main data:\")\n",
    "print(df.head())\n",
    "print(\"\\nColumns in main data:\")\n",
    "print(df.columns.tolist())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a comprehensive list of car brands and models from the reference file\n",
    "car_brands = set()\n",
    "car_models = set()\n",
    "brand_model_mapping = {}\n",
    "\n",
    "for _, row in car_models_df.iterrows():\n",
    "    if pd.notna(row['ブランド']) and pd.notna(row['モデル']):\n",
    "        brand = row['ブランド'].strip()\n",
    "        model = row['モデル'].strip()\n",
    "        car_brands.add(brand)\n",
    "        car_models.add(model)\n",
    "        \n",
    "        if brand not in brand_model_mapping:\n",
    "            brand_model_mapping[brand] = []\n",
    "        brand_model_mapping[brand].append(model)\n",
    "\n",
    "print(f\"Total unique brands: {len(car_brands)}\")\n",
    "print(f\"Total unique models: {len(car_models)}\")\n",
    "print(\"\\nSample brands:\")\n",
    "print(list(car_brands)[:10])\n",
    "print(\"\\nSample models:\")\n",
    "print(list(car_models)[:10])"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_profile_and_post(text: str) -> Tuple[str, str]:\n",
    "    \"\"\"\n",
    "    Extract profile and post content from the combined text.\n",
    "    \n",
    "    Args:\n",
    "        text: Combined text containing both profile and post sections\n",
    "        \n",
    "    Returns:\n",
    "        Tuple of (profile_content, post_content)\n",
    "    \"\"\"\n",
    "    if pd.isna(text):\n",
    "        return \"\", \"\"\n",
    "    \n",
    "    text = str(text)\n",
    "    \n",
    "    # Extract profile section\n",
    "    profile_match = re.search(r'【プロフィール】(.*?)(?=【投稿|$)', text, re.DOTALL)\n",
    "    profile_content = profile_match.group(1).strip() if profile_match else \"\"\n",
    "    \n",
    "    # Extract post content section\n",
    "    post_match = re.search(r'【投稿内容】(.*?)$', text, re.DOTALL)\n",
    "    post_content = post_match.group(1).strip() if post_match else \"\"\n",
    "    \n",
    "    return profile_content, post_content\n",
    "\n",
    "# Test the function\n",
    "sample_text = df['post_line_with_bio'].iloc[1]\n",
    "profile, post = extract_profile_and_post(sample_text)\n",
    "print(\"Sample profile:\", profile[:100] + \"...\" if len(profile) > 100 else profile)\n",
    "print(\"Sample post:\", post[:100] + \"...\" if len(post) > 100 else post)"
   ]
  }
 ],
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def create_car_analysis_prompt(profile_content: str, post_content: str, car_brands: set, car_models: set) -> str:\n",
    "    \"\"\"\n",
    "    Create a prompt for LLM to analyze car ownership from profile and post content.\n",
    "    \"\"\"\n",
    "    brands_list = \", \".join(sorted(list(car_brands))[:20])  # Limit to first 20 for prompt size\n",
    "    models_list = \", \".join(sorted(list(car_models))[:30])  # Limit to first 30 for prompt size\n",
    "    \n",
    "    prompt = f\"\"\"\n",
    "You are analyzing social media content to determine car ownership. Please analyze the following profile and post content to extract car ownership information.\n",
    "\n",
    "PROFILE CONTENT:\n",
    "{profile_content}\n",
    "\n",
    "POST CONTENT:\n",
    "{post_content}\n",
    "\n",
    "TASK:\n",
    "1. Determine if the person owns any cars based on the content\n",
    "2. Extract car brand(s) and model(s) if ownership is indicated\n",
    "3. Determine if the car information can be determined from PROFILE ONLY or requires POST CONTENT\n",
    "\n",
    "REFERENCE CAR BRANDS (examples): {brands_list}...\n",
    "REFERENCE CAR MODELS (examples): {models_list}...\n",
    "\n",
    "RULES:\n",
    "- Look for ownership indicators like \"my car\", \"I own\", \"I have\", \"I bought\", \"I purchased\", etc.\n",
    "- If multiple cars are mentioned, separate them with commas\n",
    "- Status should be \"既存\" if they currently own the car\n",
    "- Mark \"○\" for profile-only determination if car info can be determined from profile alone\n",
    "- Leave profile-only determination empty if post content is needed\n",
    "\n",
    "Please respond in JSON format:\n",
    "{{\n",
    "    \"has_car\": true/false,\n",
    "    \"cars\": [\n",
    "        {{\n",
    "            \"brand\": \"brand_name\",\n",
    "            \"model\": \"model_name\",\n",
    "            \"status\": \"既存\",\n",
    "            \"profile_only\": \"○\" or \"\"\n",
    "        }}\n",
    "    ]\n",
    "}}\n",
    "\"\"\"\n",
    "    return prompt\n",
    "\n",
    "# Test prompt creation\n",
    "sample_prompt = create_car_analysis_prompt(profile, post, car_brands, car_models)\n",
    "print(\"Sample prompt length:\", len(sample_prompt))\n",
    "print(\"First 500 characters:\", sample_prompt[:500])"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def analyze_car_ownership_with_llm(profile_content: str, post_content: str, car_brands: set, car_models: set) -> Dict:\n",
    "    \"\"\"\n",
    "    Use OpenAI API to analyze car ownership from profile and post content.\n",
    "    \"\"\"\n",
    "    prompt = create_car_analysis_prompt(profile_content, post_content, car_brands, car_models)\n",
    "    \n",
    "    try:\n",
    "        response = openai.ChatCompletion.create(\n",
    "            model=\"gpt-3.5-turbo\",  # or \"gpt-4\" if you have access\n",
    "            messages=[\n",
    "                {\"role\": \"system\", \"content\": \"You are an expert at analyzing social media content to determine car ownership. Always respond with valid JSON.\"},\n",
    "                {\"role\": \"user\", \"content\": prompt}\n",
    "            ],\n",
    "            temperature=0.1,\n",
    "            max_tokens=500\n",
    "        )\n",
    "        \n",
    "        result_text = response.choices[0].message.content.strip()\n",
    "        \n",
    "        # Try to parse JSON response\n",
    "        try:\n",
    "            result = json.loads(result_text)\n",
    "            return result\n",
    "        except json.JSONDecodeError:\n",
    "            print(f\"Failed to parse JSON: {result_text}\")\n",
    "            return {\"has_car\": False, \"cars\": []}\n",
    "            \n",
    "    except Exception as e:\n",
    "        print(f\"Error calling OpenAI API: {e}\")\n",
    "        return {\"has_car\": False, \"cars\": []}\n",
    "\n",
    "# Alternative function using rule-based approach (if you don't want to use OpenAI API)\n",
    "def analyze_car_ownership_rule_based(profile_content: str, post_content: str, car_brands: set, car_models: set) -> Dict:\n",
    "    \"\"\"\n",
    "    Rule-based approach to analyze car ownership (alternative to LLM).\n",
    "    \"\"\"\n",
    "    combined_text = (profile_content + \" \" + post_content).lower()\n",
    "    \n",
    "    # Ownership indicators\n",
    "    ownership_patterns = [\n",
    "        r'my\\s+([a-zA-Z0-9\\s]+)\\s+car',\n",
    "        r'i\\s+own\\s+([a-zA-Z0-9\\s]+)',\n",
    "        r'i\\s+have\\s+([a-zA-Z0-9\\s]+)',\n",
    "        r'i\\s+bought\\s+([a-zA-Z0-9\\s]+)',\n",
    "        r'i\\s+purchased\\s+([a-zA-Z0-9\\s]+)',\n",
    "        r'driving\\s+my\\s+([a-zA-Z0-9\\s]+)'\n",
    "    ]\n",
    "    \n",
    "    found_cars = []\n",
    "    \n",
    "    # Check for car mentions\n",
    "    for brand in car_brands:\n",
    "        if brand.lower() in combined_text:\n",
    "            for model in car_models:\n",
    "                if model.lower() in combined_text:\n",
    "                    # Check if it's in ownership context\n",
    "                    for pattern in ownership_patterns:\n",
    "                        if re.search(pattern, combined_text):\n",
    "                            profile_only = \"○\" if model.lower() in profile_content.lower() else \"\"\n",
    "                            found_cars.append({\n",
    "                                \"brand\": brand,\n",
    "                                \"model\": model,\n",
    "                                \"status\": \"既存\",\n",
    "                                \"profile_only\": profile_only\n",
    "                            })\n",
    "                            break\n",
    "    \n",
    "    return {\n",
    "        \"has_car\": len(found_cars) > 0,\n",
    "        \"cars\": found_cars\n",
    "    }\n",
    "\n",
    "print(\"Analysis functions defined!\")"
   ]
  }
 ],
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def process_dataframe(df: pd.DataFrame, use_llm: bool = False) -> pd.DataFrame:\n",
    "    \"\"\"\n",
    "    Process the entire dataframe to extract car ownership information.\n",
    "    \n",
    "    Args:\n",
    "        df: Input dataframe\n",
    "        use_llm: Whether to use LLM (True) or rule-based approach (False)\n",
    "        \n",
    "    Returns:\n",
    "        Dataframe with populated columns L, M, N, O\n",
    "    \"\"\"\n",
    "    # Initialize the new columns\n",
    "    df['ブランド'] = ''\n",
    "    df['車種'] = ''\n",
    "    df['状態'] = ''\n",
    "    df['プロフィールのみで判断'] = ''\n",
    "    \n",
    "    # Process each row\n",
    "    for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"Processing rows\"):\n",
    "        profile_content, post_content = extract_profile_and_post(row['post_line_with_bio'])\n",
    "        \n",
    "        if use_llm:\n",
    "            result = analyze_car_ownership_with_llm(profile_content, post_content, car_brands, car_models)\n",
    "        else:\n",
    "            result = analyze_car_ownership_rule_based(profile_content, post_content, car_brands, car_models)\n",
    "        \n",
    "        if result['has_car'] and result['cars']:\n",
    "            # Handle multiple cars by joining with commas\n",
    "            brands = [car['brand'] for car in result['cars']]\n",
    "            models = [car['model'] for car in result['cars']]\n",
    "            statuses = [car['status'] for car in result['cars']]\n",
    "            profile_only_flags = [car['profile_only'] for car in result['cars']]\n",
    "            \n",
    "            df.at[idx, 'ブランド'] = ', '.join(brands)\n",
    "            df.at[idx, '車種'] = ', '.join(models)\n",
    "            df.at[idx, '状態'] = ', '.join(statuses)\n",
    "            df.at[idx, 'プロフィールのみで判断'] = ', '.join(profile_only_flags)\n",
    "        \n",
    "        # Add small delay if using LLM to avoid rate limits\n",
    "        if use_llm:\n",
    "            time.sleep(0.1)\n",
    "    \n",
    "    return df\n",
    "\n",
    "print(\"Processing function defined!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test on a small sample first\n",
    "print(\"Testing on first 5 rows...\")\n",
    "sample_df = df.head(5).copy()\n",
    "\n",
    "# Use rule-based approach for testing (change to use_llm=True if you have OpenAI API key)\n",
    "processed_sample = process_dataframe(sample_df, use_llm=False)\n",
    "\n",
    "# Display results\n",
    "print(\"\\nResults for sample data:\")\n",
    "columns_to_show = ['post_line_with_bio', 'ブランド', '車種', '状態', 'プロフィールのみで判断']\n",
    "for idx, row in processed_sample.iterrows():\n",
    "    print(f\"\\nRow {idx}:\")\n",
    "    print(f\"Post content: {row['post_line_with_bio'][:100]}...\")\n",
    "    print(f\"Brand: {row['ブランド']}\")\n",
    "    print(f\"Model: {row['車種']}\")\n",
    "    print(f\"Status: {row['状態']}\")\n",
    "    print(f\"Profile only: {row['プロフィールのみで判断']}\")\n",
    "    print(\"-\" * 50)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Process the full dataset (uncomment when ready)\n",
    "# WARNING: This will take time, especially if using LLM\n",
    "\n",
    "print(\"Processing full dataset...\")\n",
    "print(\"Note: Set use_llm=True if you have OpenAI API key configured\")\n",
    "\n",
    "# Choose your approach:\n",
    "USE_LLM = False  # Set to True if you have OpenAI API key\n",
    "\n",
    "if USE_LLM:\n",
    "    print(\"Using LLM approach (requires OpenAI API key)\")\n",
    "else:\n",
    "    print(\"Using rule-based approach\")\n",
    "\n",
    "# Process the full dataframe\n",
    "processed_df = process_dataframe(df.copy(), use_llm=USE_LLM)\n",
    "\n",
    "# Save the results\n",
    "output_filename = 'データ_processed.csv'\n",
    "processed_df.to_csv(output_filename, index=False, encoding='utf-8-sig')\n",
    "print(f\"\\nResults saved to {output_filename}\")\n",
    "\n",
    "# Show summary statistics\n",
    "cars_found = processed_df[processed_df['ブランド'] != ''].shape[0]\n",
    "total_rows = processed_df.shape[0]\n",
    "print(f\"\\nSummary:\")\n",
    "print(f\"Total rows processed: {total_rows}\")\n",
    "print(f\"Rows with car ownership detected: {cars_found}\")\n",
    "print(f\"Detection rate: {cars_found/total_rows*100:.1f}%\")"
   ]
  }
 ],
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analysis and Visualization of Results\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "\n",
    "# Load the processed data if not already in memory\n",
    "# processed_df = pd.read_csv('データ_processed.csv')\n",
    "\n",
    "# Analyze brand distribution\n",
    "brand_counts = {}\n",
    "for brands_str in processed_df['ブランド']:\n",
    "    if brands_str and brands_str != '':\n",
    "        brands = [b.strip() for b in brands_str.split(',')]\n",
    "        for brand in brands:\n",
    "            if brand:\n",
    "                brand_counts[brand] = brand_counts.get(brand, 0) + 1\n",
    "\n",
    "print(\"Top 10 Car Brands Found:\")\n",
    "sorted_brands = sorted(brand_counts.items(), key=lambda x: x[1], reverse=True)\n",
    "for brand, count in sorted_brands[:10]:\n",
    "    print(f\"{brand}: {count}\")\n",
    "\n",
    "# Analyze model distribution\n",
    "model_counts = {}\n",
    "for models_str in processed_df['車種']:\n",
    "    if models_str and models_str != '':\n",
    "        models = [m.strip() for m in models_str.split(',')]\n",
    "        for model in models:\n",
    "            if model:\n",
    "                model_counts[model] = model_counts.get(model, 0) + 1\n",
    "\n",
    "print(\"\\nTop 10 Car Models Found:\")\n",
    "sorted_models = sorted(model_counts.items(), key=lambda x: x[1], reverse=True)\n",
    "for model, count in sorted_models[:10]:\n",
    "    print(f\"{model}: {count}\")\n",
    "\n",
    "# Analyze profile-only vs post-content determination\n",
    "profile_only_count = processed_df[processed_df['プロフィールのみで判断'].str.contains('○', na=False)].shape[0]\n",
    "post_content_count = processed_df[(processed_df['ブランド'] != '') & (~processed_df['プロフィールのみで判断'].str.contains('○', na=False))].shape[0]\n",
    "\n",
    "print(f\"\\nDetermination Source:\")\n",
    "print(f\"Profile only: {profile_only_count}\")\n",
    "print(f\"Requires post content: {post_content_count}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create visualizations\n",
    "plt.figure(figsize=(15, 10))\n",
    "\n",
    "# Brand distribution chart\n",
    "plt.subplot(2, 2, 1)\n",
    "if brand_counts:\n",
    "    top_brands = dict(sorted_brands[:10])\n",
    "    plt.bar(range(len(top_brands)), list(top_brands.values()))\n",
    "    plt.xticks(range(len(top_brands)), list(top_brands.keys()), rotation=45, ha='right')\n",
    "    plt.title('Top 10 Car Brands Found')\n",
    "    plt.ylabel('Count')\n",
    "\n",
    "# Model distribution chart\n",
    "plt.subplot(2, 2, 2)\n",
    "if model_counts:\n",
    "    top_models = dict(sorted_models[:10])\n",
    "    plt.bar(range(len(top_models)), list(top_models.values()))\n",
    "    plt.xticks(range(len(top_models)), list(top_models.keys()), rotation=45, ha='right')\n",
    "    plt.title('Top 10 Car Models Found')\n",
    "    plt.ylabel('Count')\n",
    "\n",
    "# Profile vs Post determination\n",
    "plt.subplot(2, 2, 3)\n",
    "determination_data = ['Profile Only', 'Requires Post Content']\n",
    "determination_counts = [profile_only_count, post_content_count]\n",
    "plt.pie(determination_counts, labels=determination_data, autopct='%1.1f%%')\n",
    "plt.title('Information Source for Car Detection')\n",
    "\n",
    "# Overall detection rate\n",
    "plt.subplot(2, 2, 4)\n",
    "detection_data = ['Cars Detected', 'No Cars Detected']\n",
    "detection_counts = [cars_found, total_rows - cars_found]\n",
    "plt.pie(detection_counts, labels=detection_data, autopct='%1.1f%%')\n",
    "plt.title('Overall Car Detection Rate')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.savefig('car_analysis_results.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"Visualizations saved as 'car_analysis_results.png'\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Instructions for Use\n",
    "\n",
    "### Setup:\n",
    "1. Install required packages: `pip install pandas openai tqdm matplotlib seaborn`\n",
    "2. If using OpenAI API, set your API key in the first cell\n",
    "3. Make sure your CSV files are in the same directory as this notebook\n",
    "\n",
    "### Running the Analysis:\n",
    "1. Run all cells in order\n",
    "2. The notebook will first test on a small sample (5 rows)\n",
    "3. Then process the full dataset\n",
    "4. Results will be saved to 'データ_processed.csv'\n",
    "\n",
    "### Output Columns:\n",
    "- **ブランド (Column L)**: Car brand names, comma-separated if multiple\n",
    "- **車種 (Column M)**: Car model names, comma-separated if multiple  \n",
    "- **状態 (Column N)**: Status (既存 for existing ownership)\n",
    "- **プロフィールのみで判断 (Column O)**: ○ if determined from profile only, empty if post content needed\n",
    "\n",
    "### Approach Options:\n",
    "- **Rule-based**: Fast, free, but may miss some cases\n",
    "- **LLM-based**: More accurate, requires OpenAI API key and costs money\n",
    "\n",
    "### Customization:\n",
    "- Modify the ownership patterns in `analyze_car_ownership_rule_based()` to improve detection\n",
    "- Adjust the LLM prompt in `create_car_analysis_prompt()` for better results\n",
    "- Add more car brands/models to the reference list if needed"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
