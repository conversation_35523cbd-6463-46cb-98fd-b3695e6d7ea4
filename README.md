﻿# Car Ownership Analyzer

This project analyzes social media posts to determine car ownership and extract brand, model, and status information from Japanese social media data.

## Features

- Extracts car ownership information from social media posts
- Separates profile (【プロフィール】) and post content (【投稿内容】) analysis
- Identifies car brands and models from a reference list
- Determines if information comes from profile only or requires post content
- Supports both rule-based and LLM-based analysis approaches
- Outputs results to CSV with Japanese column headers

## Files

- `car_ownership_analyzer.ipynb` - Jupyter notebook with full analysis pipeline
- `car_analyzer.py` - Python script version for command-line execution
- `データ.csv` - Input data file with social media posts
- `機種リスト.csv` - Reference file with car brands and models
- `requirements.txt` - Python dependencies

## Setup

1. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

2. (Optional) For LLM analysis, set up OpenAI API key:
   ```python
   import openai
   openai.api_key = "your-api-key-here"
   ```

## Usage

### Option 1: Jupyter Notebook
1. Open `car_ownership_analyzer.ipynb` in Jupyter
2. Run all cells in order
3. Results will be saved to `データ_processed.csv`

### Option 2: Python Script
```bash
python car_analyzer.py
```

## Output Columns

The analysis adds these columns to your data:

- **ブランド (Column L)**: Car brand names, comma-separated if multiple
- **車種 (Column M)**: Car model names, comma-separated if multiple  
- **状態 (Column N)**: Status (既存 for existing ownership)
- **プロフィールのみで判断 (Column O)**:  if determined from profile only, empty if post content needed

## Analysis Approaches

### Rule-based (Default)
- Fast and free
- Uses pattern matching for ownership indicators
- May miss some complex cases

### LLM-based (Optional)
- More accurate understanding of context
- Requires OpenAI API key and costs money
- Better at handling complex language patterns

## Customization

- Modify ownership patterns in `analyze_car_ownership_rule_based()` to improve detection
- Adjust the LLM prompt in `create_car_analysis_prompt()` for better results
- Add more car brands/models to the reference list if needed

## Example Results

The system will identify patterns like:
- "my Honda Civic"  Brand: Honda, Model: Civic, Status: 既存
- "I bought a new Toyota Camry"  Brand: Toyota, Model: Camry, Status: 既存
- Profile mentions car  Profile-only: 
- Post content needed  Profile-only: (empty)
